package com.walnut.vegaspread.coa.audit;

import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.repository.CoaRepository;
import jakarta.enterprise.context.ApplicationScoped;
import org.hibernate.envers.RevisionType;

/**
 * CoaEntity-specific rollback strategy implementation.
 * This handles the specific business rules and field mapping for CoaEntity rollback operations.
 */
@ApplicationScoped
public class CoaEntityRollbackStrategy implements EntityRollbackStrategy<CoaEntity, Integer> {

    private final CoaRepository coaRepository;

    public CoaEntityRollbackStrategy(CoaRepository coaRepository) {
        this.coaRepository = coaRepository;
    }

    @Override
    public Class<CoaEntity> getEntityClass() {
        return CoaEntity.class;
    }

    @Override
    public CoaEntity findById(Integer entityId) {
        return coaRepository.findById(entityId);
    }

    @Override
    public void persist(CoaEntity entity) {
        coaRepository.persist(entity);
    }

    @Override
    public void delete(Integer entityId) {
        CoaEntity entity = coaRepository.findById(entityId);
        if (entity != null) {
            coaRepository.delete(entity);
        }
    }

    @Override
    public Integer getEntityId(CoaEntity entity) {
        return entity.getCoaId();
    }

    @Override
    public CoaEntity createNewEntity() {
        return new CoaEntity();
    }

    @Override
    public void copyAuditedFields(CoaEntity source, CoaEntity target, RevisionType revisionType, boolean isEntityCreation) {
        // Copy all @Audited fields
        target.setCoaText(source.getCoaText());
        target.setCoaDescription(source.getCoaDescription());
        target.setIsActive(source.getIsActive());
        target.setLvl1Category(source.getLvl1Category());
        target.setSign(source.getSign());
        
        // Handle clientName specially since it's @NotAudited
        if (isEntityCreation) {
            // For entity creation, use the clientName from audit history
            // This should be safe as it represents the original clientName when the entity was created
            target.setClientName(source.getClientName());
        }
        // For updates, we skip clientName to preserve the current value since it's @NotAudited
        // and cannot be changed after creation
    }

    @Override
    public boolean isRollbackAllowed(Integer entityId, RevisionType revisionType) {
        // Add any CoaEntity-specific business rules here
        // For example, you might want to prevent rollback of certain entities
        // or check user permissions
        
        // For now, allow all rollbacks
        return true;
    }

    @Override
    public String getEntityTypeName() {
        return "CoaEntity";
    }
}
