package com.walnut.vegaspread.coa.audit;

import com.walnut.vegaspread.common.exceptions.ResponseException;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.RevisionType;
import org.hibernate.envers.query.AuditEntity;
import org.hibernate.envers.query.AuditQuery;
import org.jboss.logging.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Generic rollback service that can work with any entity type.
 */
@ApplicationScoped
public class GenericRollbackService {

    private static final Logger logger = Logger.getLogger(GenericRollbackService.class);

    /**
     * Generic rollback method that works with any entity type.
     * This is the main entry point for rollback operations.
     *
     * @param traceId       The trace ID to rollback to
     * @param strategy      The entity-specific rollback strategy
     * @param entityManager The entity manager
     * @param <E>           The entity type
     * @param <ID>          The entity ID type
     */
    @Transactional
    public <E, ID> void rollback(String traceId, EntityRollbackStrategy<E, ID> strategy, EntityManager entityManager) {
        logger.debugf("Rolling back trace id %s for entity type %s", traceId, strategy.getEntityTypeName());

        if (traceId == null || traceId.trim().isEmpty()) {
            ResponseException.throwResponseException(Response.Status.BAD_REQUEST, "TraceId cannot be null or empty");
        }

        AuditReader reader = AuditReaderFactory.get(entityManager);

        // Find all entity revisions with the given traceId
        AuditQuery query = reader.createQuery()
                .forRevisionsOfEntityWithChanges(strategy.getEntityClass(), true)
                .add(AuditEntity.revisionProperty("traceId").eq(traceId));

        @SuppressWarnings("unchecked")
        List<Object[]> revisions = query.getResultList();

        if (revisions.isEmpty()) {
            logger.warnf("No revisions found for traceId %s and entity type %s", traceId, strategy.getEntityTypeName());
            return;
        }

        // Group revisions by entity ID to handle multiple changes to the same entity
        Map<ID, EntityRevisionInfo<E>> entityRevisions = new HashMap<>();

        for (Object[] revision : revisions) {
            E entity = (E) revision[0];
            MetadataRevEntity revEntity = (MetadataRevEntity) revision[1];
            RevisionType revType = (RevisionType) revision[2];

            ID entityId = strategy.getEntityId(entity);

            // Store the revision info for this entity (last revision wins for same entity)
            entityRevisions.put(entityId, new EntityRevisionInfo<>(entity, revEntity, revType));
        }

        logger.infof("Found %d entities to rollback for traceId %s", entityRevisions.size(), traceId);

        // Process each entity
        for (Map.Entry<ID, EntityRevisionInfo<E>> entry : entityRevisions.entrySet()) {
            ID entityId = entry.getKey();
            EntityRevisionInfo<E> revisionInfo = entry.getValue();

            rollbackEntity(entityId, revisionInfo, strategy, reader);
        }

        logger.infof("Successfully rolled back %d entities for traceId %s", entityRevisions.size(), traceId);
    }

    /**
     * Rollback a single entity to the state of the selected traceId.
     */
    private <E, ID> void rollbackEntity(ID entityId, EntityRevisionInfo<E> revisionInfo,
                                        EntityRollbackStrategy<E, ID> strategy, AuditReader reader) {
        try {
            RevisionType revisionType = revisionInfo.revisionType();

            // Check if rollback is allowed
            if (!strategy.isRollbackAllowed(entityId, revisionType)) {
                logger.warnf("Rollback not allowed for entity %s of type %s with revision type %s",
                        entityId, strategy.getEntityTypeName(), revisionType);
                return;
            }

            // Get the revision number for our traceId revision
            Number targetRevisionNumber = revisionInfo.revisionEntity().getId();

            if (revisionType == RevisionType.DEL) {
                // If the traceId represents a deletion, we need to restore the entity to its state
                // just before it was deleted (which is the last existing state)
                handleEntityDeletedInTraceId(entityId, targetRevisionNumber, strategy, reader);
            } else {
                // For ADD and MOD operations, restore entity to the target traceId state
                restoreEntityToTargetState(entityId, targetRevisionNumber, revisionInfo.entity(), strategy,
                        revisionType);
            }
        } catch (Exception e) {
            logger.errorf(e, "Failed to rollback entity %s of type %s", entityId, strategy.getEntityTypeName());
            throw new RuntimeException(
                    "Failed to rollback entity " + entityId + " of type " + strategy.getEntityTypeName(), e);
        }
    }

    /**
     * Handle an entity that was deleted in the traceId revision by recreating it.
     */
    private <E, ID> void handleEntityDeletedInTraceId(ID entityId, Number targetRevisionNumber,
                                                      EntityRollbackStrategy<E, ID> strategy, AuditReader reader) {
        // Get all revisions for this entity to find the state before deletion
        List<Number> revisionNumbers = reader.getRevisions(strategy.getEntityClass(), entityId);

        // Find the revision before the deletion
        Number previousRevisionNumber = null;
        for (int i = 0; i < revisionNumbers.size(); i++) {
            if (revisionNumbers.get(i).equals(targetRevisionNumber) && i > 0) {
                previousRevisionNumber = revisionNumbers.get(i - 1);
                break;
            }
        }

        if (previousRevisionNumber == null) {
            logger.warnf("Entity %s was deleted in traceId but no previous revision found to restore", entityId);
            return;
        }

        // Get the entity state before deletion
        E stateBeforeDeletion = reader.find(strategy.getEntityClass(), entityId, previousRevisionNumber);

        if (stateBeforeDeletion == null) {
            logger.errorf("Could not find state before deletion for entity %s at revision %s", entityId,
                    previousRevisionNumber);
            return;
        }

        // Use the strategy to handle deletion rollback
        E restoredEntity = strategy.handleDeletionRollback(entityId, stateBeforeDeletion, reader);

        if (restoredEntity != null) {
            logger.infof("Successfully handled deletion rollback for entity %s at revision %s", entityId,
                    previousRevisionNumber);
        }
    }

    /**
     * Restore an entity to the target revision state.
     */
    private <E, ID> void restoreEntityToTargetState(ID entityId, Number targetRevisionNumber, E targetState,
                                                    EntityRollbackStrategy<E, ID> strategy, RevisionType revisionType) {
        E currentEntity = strategy.findById(entityId);

        if (currentEntity == null) {
            // Entity doesn't exist, create it with the target state
            E restoredEntity = strategy.createNewEntity();
            strategy.copyAuditedFields(targetState, restoredEntity, revisionType, true);
            strategy.persist(restoredEntity);
            logger.infof("Successfully created entity %s with target state at revision %s", entityId,
                    targetRevisionNumber);
        } else {
            // Copy audited fields from target state to current entity
            strategy.copyAuditedFields(targetState, currentEntity, revisionType, false);
            strategy.persist(currentEntity);
            logger.infof("Successfully restored entity %s to target revision %s", entityId, targetRevisionNumber);
        }
    }

    /**
     * Record to hold revision information.
     */
    private record EntityRevisionInfo<E>(
            E entity,
            MetadataRevEntity revisionEntity,
            RevisionType revisionType
    ) {
    }
}
